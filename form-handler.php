<?php
/**
 * WhatsApp OTP Form Handler
 * Handles form rendering, validation, and user logic
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WhatsApp_OTP_Form_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Constructor can be used for hooks if needed
    }
    
    /**
     * Render the OTP login form
     */
    public function render_form() {
        ob_start();
        
        // Get user's existing phone if logged in or from WooCommerce
        $existing_phone = $this->get_user_phone();
        $country_code = '';
        $phone_number = '';
        
        if ($existing_phone) {
            $parsed = $this->parse_phone_number($existing_phone);
            $country_code = $parsed['country_code'];
            $phone_number = $parsed['phone_number'];
        }
        
        ?>
        <div class="whatsapp-otp-form">
            <h3><?php _e('Login with WhatsApp OTP', 'whatsapp-otp-login'); ?></h3>
            
            <div id="otp-messages"></div>
            
            <!-- Step 1: Phone Number Input -->
            <div class="step active" id="step-phone">
                <form id="phone-form">
                    <?php wp_nonce_field('whatsapp_otp_nonce', 'whatsapp_otp_nonce'); ?>
                    
                    <div class="form-group">
                        <label for="country-select"><?php _e('Country', 'whatsapp-otp-login'); ?></label>
                        <div class="phone-input-group">
                            <div class="country-select">
                                <select id="country-select" name="country_code" required>
                                    <option value=""><?php _e('Select Country', 'whatsapp-otp-login'); ?></option>
                                    <?php echo $this->get_country_options($country_code); ?>
                                </select>
                            </div>
                            <div class="phone-input">
                                <input type="tel" id="phone-number" name="phone_number" 
                                       placeholder="<?php _e('Phone Number', 'whatsapp-otp-login'); ?>" 
                                       value="<?php echo esc_attr($phone_number); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" id="send-otp-btn">
                        <?php _e('Send OTP via WhatsApp', 'whatsapp-otp-login'); ?>
                    </button>
                </form>
            </div>
            
            <!-- Step 2: OTP Verification -->
            <div class="step" id="step-otp">
                <button type="button" class="back-btn" id="back-to-phone">
                    <?php _e('← Back to Phone', 'whatsapp-otp-login'); ?>
                </button>
                
                <form id="otp-form">
                    <?php wp_nonce_field('whatsapp_otp_nonce', 'whatsapp_otp_nonce_verify'); ?>
                    <input type="hidden" id="verified-phone" name="verified_phone" value="">
                    
                    <div class="form-group">
                        <label for="otp-code"><?php _e('Enter OTP Code', 'whatsapp-otp-login'); ?></label>
                        <input type="text" id="otp-code" name="otp_code" 
                               placeholder="<?php _e('6-digit code', 'whatsapp-otp-login'); ?>" 
                               maxlength="6" pattern="[0-9]{6}" required>
                        <small><?php _e('Check your WhatsApp for the verification code', 'whatsapp-otp-login'); ?></small>
                    </div>
                    
                    <button type="submit" id="verify-otp-btn">
                        <?php _e('Verify & Login', 'whatsapp-otp-login'); ?>
                    </button>
                </form>
                
                <div style="text-align: center; margin-top: 15px;">
                    <button type="button" id="resend-otp" class="back-btn" style="width: auto; padding: 8px 16px;">
                        <?php _e('Resend OTP', 'whatsapp-otp-login'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Get country options for dropdown
     */
    private function get_country_options($selected_code = '') {
        $countries = $this->get_countries_data();
        $options = '';
        
        foreach ($countries as $code => $country) {
            $selected = ($code === $selected_code) ? 'selected' : '';
            $options .= sprintf(
                '<option value="%s" data-code="%s" %s>%s %s (+%s)</option>',
                esc_attr($code),
                esc_attr($country['code']),
                $selected,
                $country['flag'],
                esc_html($country['name']),
                esc_html($country['code'])
            );
        }
        
        return $options;
    }
    
    /**
     * Get countries data with flags and codes
     */
    private function get_countries_data() {
        return array(
            'US' => array('name' => 'United States', 'code' => '1', 'flag' => '🇺🇸'),
            'GB' => array('name' => 'United Kingdom', 'code' => '44', 'flag' => '🇬🇧'),
            'CA' => array('name' => 'Canada', 'code' => '1', 'flag' => '🇨🇦'),
            'AU' => array('name' => 'Australia', 'code' => '61', 'flag' => '🇦🇺'),
            'DE' => array('name' => 'Germany', 'code' => '49', 'flag' => '🇩🇪'),
            'FR' => array('name' => 'France', 'code' => '33', 'flag' => '🇫🇷'),
            'IT' => array('name' => 'Italy', 'code' => '39', 'flag' => '🇮🇹'),
            'ES' => array('name' => 'Spain', 'code' => '34', 'flag' => '🇪🇸'),
            'NL' => array('name' => 'Netherlands', 'code' => '31', 'flag' => '🇳🇱'),
            'BE' => array('name' => 'Belgium', 'code' => '32', 'flag' => '🇧🇪'),
            'CH' => array('name' => 'Switzerland', 'code' => '41', 'flag' => '🇨🇭'),
            'AT' => array('name' => 'Austria', 'code' => '43', 'flag' => '🇦🇹'),
            'SE' => array('name' => 'Sweden', 'code' => '46', 'flag' => '🇸🇪'),
            'NO' => array('name' => 'Norway', 'code' => '47', 'flag' => '🇳🇴'),
            'DK' => array('name' => 'Denmark', 'code' => '45', 'flag' => '🇩🇰'),
            'FI' => array('name' => 'Finland', 'code' => '358', 'flag' => '🇫🇮'),
            'PL' => array('name' => 'Poland', 'code' => '48', 'flag' => '🇵🇱'),
            'CZ' => array('name' => 'Czech Republic', 'code' => '420', 'flag' => '🇨🇿'),
            'HU' => array('name' => 'Hungary', 'code' => '36', 'flag' => '🇭🇺'),
            'RO' => array('name' => 'Romania', 'code' => '40', 'flag' => '🇷🇴'),
            'BG' => array('name' => 'Bulgaria', 'code' => '359', 'flag' => '🇧🇬'),
            'GR' => array('name' => 'Greece', 'code' => '30', 'flag' => '🇬🇷'),
            'PT' => array('name' => 'Portugal', 'code' => '351', 'flag' => '🇵🇹'),
            'IE' => array('name' => 'Ireland', 'code' => '353', 'flag' => '🇮🇪'),
            'LU' => array('name' => 'Luxembourg', 'code' => '352', 'flag' => '🇱🇺'),
            'MT' => array('name' => 'Malta', 'code' => '356', 'flag' => '🇲🇹'),
            'CY' => array('name' => 'Cyprus', 'code' => '357', 'flag' => '🇨🇾'),
            'EE' => array('name' => 'Estonia', 'code' => '372', 'flag' => '🇪🇪'),
            'LV' => array('name' => 'Latvia', 'code' => '371', 'flag' => '🇱🇻'),
            'LT' => array('name' => 'Lithuania', 'code' => '370', 'flag' => '🇱🇹'),
            'SK' => array('name' => 'Slovakia', 'code' => '421', 'flag' => '🇸🇰'),
            'SI' => array('name' => 'Slovenia', 'code' => '386', 'flag' => '🇸🇮'),
            'HR' => array('name' => 'Croatia', 'code' => '385', 'flag' => '🇭🇷'),
            'RS' => array('name' => 'Serbia', 'code' => '381', 'flag' => '🇷🇸'),
            'BA' => array('name' => 'Bosnia and Herzegovina', 'code' => '387', 'flag' => '🇧🇦'),
            'ME' => array('name' => 'Montenegro', 'code' => '382', 'flag' => '🇲🇪'),
            'MK' => array('name' => 'North Macedonia', 'code' => '389', 'flag' => '🇲🇰'),
            'AL' => array('name' => 'Albania', 'code' => '355', 'flag' => '🇦🇱'),
            'RU' => array('name' => 'Russia', 'code' => '7', 'flag' => '🇷🇺'),
            'UA' => array('name' => 'Ukraine', 'code' => '380', 'flag' => '🇺🇦'),
            'BY' => array('name' => 'Belarus', 'code' => '375', 'flag' => '🇧🇾'),
            'MD' => array('name' => 'Moldova', 'code' => '373', 'flag' => '🇲🇩'),
            'TR' => array('name' => 'Turkey', 'code' => '90', 'flag' => '🇹🇷'),
            'EG' => array('name' => 'Egypt', 'code' => '20', 'flag' => '🇪🇬'),
            'SA' => array('name' => 'Saudi Arabia', 'code' => '966', 'flag' => '🇸🇦'),
            'AE' => array('name' => 'United Arab Emirates', 'code' => '971', 'flag' => '🇦🇪'),
            'QA' => array('name' => 'Qatar', 'code' => '974', 'flag' => '🇶🇦'),
            'KW' => array('name' => 'Kuwait', 'code' => '965', 'flag' => '🇰🇼'),
            'BH' => array('name' => 'Bahrain', 'code' => '973', 'flag' => '🇧🇭'),
            'OM' => array('name' => 'Oman', 'code' => '968', 'flag' => '🇴🇲'),
            'JO' => array('name' => 'Jordan', 'code' => '962', 'flag' => '🇯🇴'),
            'LB' => array('name' => 'Lebanon', 'code' => '961', 'flag' => '🇱🇧'),
            'SY' => array('name' => 'Syria', 'code' => '963', 'flag' => '🇸🇾'),
            'IQ' => array('name' => 'Iraq', 'code' => '964', 'flag' => '🇮🇶'),
            'IR' => array('name' => 'Iran', 'code' => '98', 'flag' => '🇮🇷'),
            'IL' => array('name' => 'Israel', 'code' => '972', 'flag' => '🇮🇱'),
            'PS' => array('name' => 'Palestine', 'code' => '970', 'flag' => '🇵🇸'),
            'IN' => array('name' => 'India', 'code' => '91', 'flag' => '🇮🇳'),
            'PK' => array('name' => 'Pakistan', 'code' => '92', 'flag' => '🇵🇰'),
            'BD' => array('name' => 'Bangladesh', 'code' => '880', 'flag' => '🇧🇩'),
            'LK' => array('name' => 'Sri Lanka', 'code' => '94', 'flag' => '🇱🇰'),
            'NP' => array('name' => 'Nepal', 'code' => '977', 'flag' => '🇳🇵'),
            'BT' => array('name' => 'Bhutan', 'code' => '975', 'flag' => '🇧🇹'),
            'MV' => array('name' => 'Maldives', 'code' => '960', 'flag' => '🇲🇻'),
            'CN' => array('name' => 'China', 'code' => '86', 'flag' => '🇨🇳'),
            'JP' => array('name' => 'Japan', 'code' => '81', 'flag' => '🇯🇵'),
            'KR' => array('name' => 'South Korea', 'code' => '82', 'flag' => '🇰🇷'),
            'KP' => array('name' => 'North Korea', 'code' => '850', 'flag' => '🇰🇵'),
            'MN' => array('name' => 'Mongolia', 'code' => '976', 'flag' => '🇲🇳'),
            'TW' => array('name' => 'Taiwan', 'code' => '886', 'flag' => '🇹🇼'),
            'HK' => array('name' => 'Hong Kong', 'code' => '852', 'flag' => '🇭🇰'),
            'MO' => array('name' => 'Macau', 'code' => '853', 'flag' => '🇲🇴'),
            'SG' => array('name' => 'Singapore', 'code' => '65', 'flag' => '🇸🇬'),
            'MY' => array('name' => 'Malaysia', 'code' => '60', 'flag' => '🇲🇾'),
            'TH' => array('name' => 'Thailand', 'code' => '66', 'flag' => '🇹🇭'),
            'VN' => array('name' => 'Vietnam', 'code' => '84', 'flag' => '🇻🇳'),
            'PH' => array('name' => 'Philippines', 'code' => '63', 'flag' => '🇵🇭'),
            'ID' => array('name' => 'Indonesia', 'code' => '62', 'flag' => '🇮🇩'),
            'BN' => array('name' => 'Brunei', 'code' => '673', 'flag' => '🇧🇳'),
            'KH' => array('name' => 'Cambodia', 'code' => '855', 'flag' => '🇰🇭'),
            'LA' => array('name' => 'Laos', 'code' => '856', 'flag' => '🇱🇦'),
            'MM' => array('name' => 'Myanmar', 'code' => '95', 'flag' => '🇲🇲'),
            'BR' => array('name' => 'Brazil', 'code' => '55', 'flag' => '🇧🇷'),
            'AR' => array('name' => 'Argentina', 'code' => '54', 'flag' => '🇦🇷'),
            'CL' => array('name' => 'Chile', 'code' => '56', 'flag' => '🇨🇱'),
            'CO' => array('name' => 'Colombia', 'code' => '57', 'flag' => '🇨🇴'),
            'PE' => array('name' => 'Peru', 'code' => '51', 'flag' => '🇵🇪'),
            'VE' => array('name' => 'Venezuela', 'code' => '58', 'flag' => '🇻🇪'),
            'EC' => array('name' => 'Ecuador', 'code' => '593', 'flag' => '🇪🇨'),
            'BO' => array('name' => 'Bolivia', 'code' => '591', 'flag' => '🇧🇴'),
            'PY' => array('name' => 'Paraguay', 'code' => '595', 'flag' => '🇵🇾'),
            'UY' => array('name' => 'Uruguay', 'code' => '598', 'flag' => '🇺🇾'),
            'GY' => array('name' => 'Guyana', 'code' => '592', 'flag' => '🇬🇾'),
            'SR' => array('name' => 'Suriname', 'code' => '597', 'flag' => '🇸🇷'),
            'GF' => array('name' => 'French Guiana', 'code' => '594', 'flag' => '🇬🇫'),
            'MX' => array('name' => 'Mexico', 'code' => '52', 'flag' => '🇲🇽'),
            'GT' => array('name' => 'Guatemala', 'code' => '502', 'flag' => '🇬🇹'),
            'BZ' => array('name' => 'Belize', 'code' => '501', 'flag' => '🇧🇿'),
            'SV' => array('name' => 'El Salvador', 'code' => '503', 'flag' => '🇸🇻'),
            'HN' => array('name' => 'Honduras', 'code' => '504', 'flag' => '🇭🇳'),
            'NI' => array('name' => 'Nicaragua', 'code' => '505', 'flag' => '🇳🇮'),
            'CR' => array('name' => 'Costa Rica', 'code' => '506', 'flag' => '🇨🇷'),
            'PA' => array('name' => 'Panama', 'code' => '507', 'flag' => '🇵🇦'),
            'ZA' => array('name' => 'South Africa', 'code' => '27', 'flag' => '🇿🇦'),
            'NG' => array('name' => 'Nigeria', 'code' => '234', 'flag' => '🇳🇬'),
            'KE' => array('name' => 'Kenya', 'code' => '254', 'flag' => '🇰🇪'),
            'GH' => array('name' => 'Ghana', 'code' => '233', 'flag' => '🇬🇭'),
            'ET' => array('name' => 'Ethiopia', 'code' => '251', 'flag' => '🇪🇹'),
            'TZ' => array('name' => 'Tanzania', 'code' => '255', 'flag' => '🇹🇿'),
            'UG' => array('name' => 'Uganda', 'code' => '256', 'flag' => '🇺🇬'),
            'RW' => array('name' => 'Rwanda', 'code' => '250', 'flag' => '🇷🇼'),
            'MA' => array('name' => 'Morocco', 'code' => '212', 'flag' => '🇲🇦'),
            'DZ' => array('name' => 'Algeria', 'code' => '213', 'flag' => '🇩🇿'),
            'TN' => array('name' => 'Tunisia', 'code' => '216', 'flag' => '🇹🇳'),
            'LY' => array('name' => 'Libya', 'code' => '218', 'flag' => '🇱🇾'),
            'SD' => array('name' => 'Sudan', 'code' => '249', 'flag' => '🇸🇩'),
        );
    }
    
    /**
     * Get user's existing phone number
     */
    private function get_user_phone() {
        if (!is_user_logged_in()) {
            return '';
        }
        
        $user_id = get_current_user_id();
        
        // Try to get from WooCommerce billing phone
        if (function_exists('get_user_meta')) {
            $billing_phone = get_user_meta($user_id, 'billing_phone', true);
            if ($billing_phone) {
                return $billing_phone;
            }
        }
        
        return '';
    }
    
    /**
     * Parse phone number to extract country code and number
     */
    private function parse_phone_number($phone) {
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        if (strpos($phone, '+') === 0) {
            $phone = substr($phone, 1);
        }
        
        $countries = $this->get_countries_data();
        
        foreach ($countries as $country_iso => $country_data) {
            $code = $country_data['code'];
            if (strpos($phone, $code) === 0) {
                return array(
                    'country_code' => $country_iso,
                    'phone_number' => substr($phone, strlen($code))
                );
            }
        }
        
        return array('country_code' => '', 'phone_number' => $phone);
    }
    
    /**
     * Validate phone number format
     */
    public function validate_phone($country_code, $phone_number) {
        // Remove any non-numeric characters
        $phone_number = preg_replace('/[^0-9]/', '', $phone_number);
        
        // Check if phone number is not empty and has reasonable length
        if (empty($phone_number) || strlen($phone_number) < 7 || strlen($phone_number) > 15) {
            return false;
        }
        
        // Check if country code exists
        $countries = $this->get_countries_data();
        if (!isset($countries[$country_code])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate secure OTP
     */
    public function generate_otp() {
        return sprintf('%06d', wp_rand(100000, 999999));
    }
    
    /**
     * Store OTP in transient
     */
    public function store_otp($phone, $otp) {
        $key = 'whatsapp_otp_' . md5($phone);
        set_transient($key, $otp, 5 * MINUTE_IN_SECONDS); // 5 minutes
    }
    
    /**
     * Verify OTP
     */
    public function verify_otp($phone, $entered_otp) {
        $key = 'whatsapp_otp_' . md5($phone);
        $stored_otp = get_transient($key);
        
        if ($stored_otp && $stored_otp === $entered_otp) {
            delete_transient($key);
            return true;
        }
        
        return false;
    }
    
    /**
     * Create or login user
     */
    public function handle_user_login($phone) {
        // Check if user exists with this phone
        $users = get_users(array(
            'meta_key' => 'billing_phone',
            'meta_value' => $phone,
            'number' => 1
        ));
        
        if (!empty($users)) {
            // User exists, log them in
            $user = $users[0];
            wp_set_current_user($user->ID);
            wp_set_auth_cookie($user->ID);
            return array('success' => true, 'message' => __('Login successful!', 'whatsapp-otp-login'));
        } else {
            // Create new user
            $username = 'user_' . substr($phone, -8) . '_' . wp_rand(1000, 9999);
            $email = $username . '@temp.local'; // Temporary email
            
            $user_id = wp_create_user($username, wp_generate_password(), $email);
            
            if (is_wp_error($user_id)) {
                return array('success' => false, 'message' => __('Failed to create user account.', 'whatsapp-otp-login'));
            }
            
            // Update user meta with phone
            update_user_meta($user_id, 'billing_phone', $phone);
            
            // Log in the new user
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
            
            return array('success' => true, 'message' => __('Account created and login successful!', 'whatsapp-otp-login'));
        }
    }
    
    /**
     * Log activity
     */
    public function log_activity($phone, $action, $status) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'whatsapp_otp_logs';
        
        $wpdb->insert(
            $table_name,
            array(
                'phone' => sanitize_text_field($phone),
                'action' => sanitize_text_field($action),
                'status' => sanitize_text_field($status),
                'ip_address' => $this->get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Check rate limiting
     */
    public function check_rate_limit($phone) {
        $key = 'whatsapp_otp_attempts_' . md5($phone);
        $attempts = get_transient($key);
        
        if ($attempts && $attempts >= 3) {
            return false; // Rate limited
        }
        
        return true;
    }
    
    /**
     * Increment rate limit counter
     */
    public function increment_rate_limit($phone) {
        $key = 'whatsapp_otp_attempts_' . md5($phone);
        $attempts = get_transient($key) ?: 0;
        $attempts++;
        
        set_transient($key, $attempts, 15 * MINUTE_IN_SECONDS); // 15 minutes
    }
}
