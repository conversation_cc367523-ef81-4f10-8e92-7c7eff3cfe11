<?php
/**
 * WhatsApp OTP AJAX Handler
 * Processes AJAX requests for OTP sending and verification
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WhatsApp_OTP_Ajax_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_send_whatsapp_otp', array($this, 'send_otp'));
        add_action('wp_ajax_nopriv_send_whatsapp_otp', array($this, 'send_otp'));
        
        add_action('wp_ajax_verify_whatsapp_otp', array($this, 'verify_otp'));
        add_action('wp_ajax_nopriv_verify_whatsapp_otp', array($this, 'verify_otp'));
    }
    
    /**
     * Send OTP via WhatsApp
     */
    public function send_otp() {
        // Verify nonce
        if (!check_ajax_referer('whatsapp_otp_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed.', 'whatsapp-otp-login')));
        }
        
        // Get and sanitize input
        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        $phone_number = sanitize_text_field($_POST['phone_number'] ?? '');
        
        // Initialize form handler
        $form_handler = new WhatsApp_OTP_Form_Handler();
        
        // Validate input
        if (!$form_handler->validate_phone($country_code, $phone_number)) {
            wp_send_json_error(array('message' => __('Please enter a valid phone number.', 'whatsapp-otp-login')));
        }
        
        // Get country data
        $countries = $this->get_countries_data();
        if (!isset($countries[$country_code])) {
            wp_send_json_error(array('message' => __('Invalid country selected.', 'whatsapp-otp-login')));
        }
        
        // Format full phone number
        $country_dial_code = $countries[$country_code]['code'];
        $full_phone = $country_dial_code . preg_replace('/[^0-9]/', '', $phone_number);
        
        // Check rate limiting
        if (!$form_handler->check_rate_limit($full_phone)) {
            wp_send_json_error(array('message' => __('Too many attempts. Please try again in 15 minutes.', 'whatsapp-otp-login')));
        }
        
        // Generate OTP
        $otp = $form_handler->generate_otp();
        
        // Send OTP via WhatsApp API
        $api_response = $this->send_whatsapp_message($full_phone, $otp);
        
        if ($api_response['success']) {
            // Store OTP
            $form_handler->store_otp($full_phone, $otp);
            
            // Log successful attempt
            $form_handler->log_activity($full_phone, 'send_otp', 'success');
            
            wp_send_json_success(array(
                'message' => __('OTP sent successfully! Check your WhatsApp.', 'whatsapp-otp-login'),
                'phone' => $full_phone
            ));
        } else {
            // Increment rate limit on failure
            $form_handler->increment_rate_limit($full_phone);
            
            // Log failed attempt
            $form_handler->log_activity($full_phone, 'send_otp', 'failed');
            
            wp_send_json_error(array('message' => $api_response['message']));
        }
    }
    
    /**
     * Verify OTP and login user
     */
    public function verify_otp() {
        // Verify nonce
        if (!check_ajax_referer('whatsapp_otp_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed.', 'whatsapp-otp-login')));
        }
        
        // Get and sanitize input
        $phone = sanitize_text_field($_POST['phone'] ?? '');
        $otp_code = sanitize_text_field($_POST['otp_code'] ?? '');
        
        // Validate input
        if (empty($phone) || empty($otp_code)) {
            wp_send_json_error(array('message' => __('Phone number and OTP are required.', 'whatsapp-otp-login')));
        }
        
        // Validate OTP format
        if (!preg_match('/^[0-9]{6}$/', $otp_code)) {
            wp_send_json_error(array('message' => __('Please enter a valid 6-digit OTP.', 'whatsapp-otp-login')));
        }
        
        // Initialize form handler
        $form_handler = new WhatsApp_OTP_Form_Handler();
        
        // Verify OTP
        if ($form_handler->verify_otp($phone, $otp_code)) {
            // OTP is valid, handle user login
            $login_result = $form_handler->handle_user_login($phone);
            
            if ($login_result['success']) {
                // Log successful verification
                $form_handler->log_activity($phone, 'verify_otp', 'success');
                
                wp_send_json_success(array(
                    'message' => $login_result['message'],
                    'redirect' => $this->get_redirect_url()
                ));
            } else {
                // Log failed login
                $form_handler->log_activity($phone, 'verify_otp', 'login_failed');
                
                wp_send_json_error(array('message' => $login_result['message']));
            }
        } else {
            // Log failed verification
            $form_handler->log_activity($phone, 'verify_otp', 'invalid_otp');
            
            wp_send_json_error(array('message' => __('Invalid or expired OTP. Please try again.', 'whatsapp-otp-login')));
        }
    }
    
    /**
     * Send WhatsApp message via API
     */
    private function send_whatsapp_message($phone, $otp) {
        // Get API settings
        $api_url = get_option('whatsapp_otp_api_url', '');
        $api_session = get_option('whatsapp_otp_api_session', '');
        
        if (empty($api_url) || empty($api_session)) {
            return array(
                'success' => false,
                'message' => __('WhatsApp API is not configured. Please contact administrator.', 'whatsapp-otp-login')
            );
        }
        
        // Prepare API endpoint
        $endpoint = rtrim($api_url, '/') . '/message/send-text';
        
        // Prepare message
        $message = sprintf(
            __('Your WhatsApp OTP Login verification code is: %s\n\nThis code will expire in 5 minutes.\n\nIf you did not request this code, please ignore this message.', 'whatsapp-otp-login'),
            $otp
        );
        
        // Prepare request data
        $request_data = array(
            'session' => $api_session,
            'to' => $phone, // Phone number without plus sign
            'text' => $message
        );
        
        // Make API request
        $response = wp_remote_post($endpoint, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($request_data)
        ));
        
        // Check for errors
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => __('Failed to connect to WhatsApp API. Please try again.', 'whatsapp-otp-login')
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        // Log API response for debugging (in development)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('WhatsApp API Response: ' . $response_body);
        }
        
        if ($response_code === 200) {
            $response_data = json_decode($response_body, true);
            
            // Check if the API response indicates success
            // This may vary depending on your WhatsApp API provider
            if (isset($response_data['success']) && $response_data['success']) {
                return array('success' => true, 'message' => 'OTP sent successfully');
            } elseif (isset($response_data['status']) && $response_data['status'] === 'success') {
                return array('success' => true, 'message' => 'OTP sent successfully');
            } else {
                return array(
                    'success' => false,
                    'message' => __('Failed to send OTP. Please check your phone number and try again.', 'whatsapp-otp-login')
                );
            }
        } else {
            return array(
                'success' => false,
                'message' => sprintf(
                    __('WhatsApp API error (Code: %d). Please try again.', 'whatsapp-otp-login'),
                    $response_code
                )
            );
        }
    }
    
    /**
     * Get redirect URL after successful login
     */
    private function get_redirect_url() {
        // Check if there's a redirect_to parameter
        if (isset($_REQUEST['redirect_to']) && !empty($_REQUEST['redirect_to'])) {
            return esc_url_raw($_REQUEST['redirect_to']);
        }
        
        // Check for WooCommerce account page
        if (function_exists('wc_get_account_endpoint_url')) {
            return wc_get_account_endpoint_url('dashboard');
        }
        
        // Default to home page
        return home_url();
    }
    
    /**
     * Get countries data (same as form handler)
     */
    private function get_countries_data() {
        return array(
            'US' => array('name' => 'United States', 'code' => '1', 'flag' => '🇺🇸'),
            'GB' => array('name' => 'United Kingdom', 'code' => '44', 'flag' => '🇬🇧'),
            'CA' => array('name' => 'Canada', 'code' => '1', 'flag' => '🇨🇦'),
            'AU' => array('name' => 'Australia', 'code' => '61', 'flag' => '🇦🇺'),
            'DE' => array('name' => 'Germany', 'code' => '49', 'flag' => '🇩🇪'),
            'FR' => array('name' => 'France', 'code' => '33', 'flag' => '🇫🇷'),
            'IT' => array('name' => 'Italy', 'code' => '39', 'flag' => '🇮🇹'),
            'ES' => array('name' => 'Spain', 'code' => '34', 'flag' => '🇪🇸'),
            'NL' => array('name' => 'Netherlands', 'code' => '31', 'flag' => '🇳🇱'),
            'BE' => array('name' => 'Belgium', 'code' => '32', 'flag' => '🇧🇪'),
            'CH' => array('name' => 'Switzerland', 'code' => '41', 'flag' => '🇨🇭'),
            'AT' => array('name' => 'Austria', 'code' => '43', 'flag' => '🇦🇹'),
            'SE' => array('name' => 'Sweden', 'code' => '46', 'flag' => '🇸🇪'),
            'NO' => array('name' => 'Norway', 'code' => '47', 'flag' => '🇳🇴'),
            'DK' => array('name' => 'Denmark', 'code' => '45', 'flag' => '🇩🇰'),
            'FI' => array('name' => 'Finland', 'code' => '358', 'flag' => '🇫🇮'),
            'PL' => array('name' => 'Poland', 'code' => '48', 'flag' => '🇵🇱'),
            'CZ' => array('name' => 'Czech Republic', 'code' => '420', 'flag' => '🇨🇿'),
            'HU' => array('name' => 'Hungary', 'code' => '36', 'flag' => '🇭🇺'),
            'RO' => array('name' => 'Romania', 'code' => '40', 'flag' => '🇷🇴'),
            'BG' => array('name' => 'Bulgaria', 'code' => '359', 'flag' => '🇧🇬'),
            'GR' => array('name' => 'Greece', 'code' => '30', 'flag' => '🇬🇷'),
            'PT' => array('name' => 'Portugal', 'code' => '351', 'flag' => '🇵🇹'),
            'IE' => array('name' => 'Ireland', 'code' => '353', 'flag' => '🇮🇪'),
            'LU' => array('name' => 'Luxembourg', 'code' => '352', 'flag' => '🇱🇺'),
            'MT' => array('name' => 'Malta', 'code' => '356', 'flag' => '🇲🇹'),
            'CY' => array('name' => 'Cyprus', 'code' => '357', 'flag' => '🇨🇾'),
            'EE' => array('name' => 'Estonia', 'code' => '372', 'flag' => '🇪🇪'),
            'LV' => array('name' => 'Latvia', 'code' => '371', 'flag' => '🇱🇻'),
            'LT' => array('name' => 'Lithuania', 'code' => '370', 'flag' => '🇱🇹'),
            'SK' => array('name' => 'Slovakia', 'code' => '421', 'flag' => '🇸🇰'),
            'SI' => array('name' => 'Slovenia', 'code' => '386', 'flag' => '🇸🇮'),
            'HR' => array('name' => 'Croatia', 'code' => '385', 'flag' => '🇭🇷'),
            'RS' => array('name' => 'Serbia', 'code' => '381', 'flag' => '🇷🇸'),
            'BA' => array('name' => 'Bosnia and Herzegovina', 'code' => '387', 'flag' => '🇧🇦'),
            'ME' => array('name' => 'Montenegro', 'code' => '382', 'flag' => '🇲🇪'),
            'MK' => array('name' => 'North Macedonia', 'code' => '389', 'flag' => '🇲🇰'),
            'AL' => array('name' => 'Albania', 'code' => '355', 'flag' => '🇦🇱'),
            'RU' => array('name' => 'Russia', 'code' => '7', 'flag' => '🇷🇺'),
            'UA' => array('name' => 'Ukraine', 'code' => '380', 'flag' => '🇺🇦'),
            'BY' => array('name' => 'Belarus', 'code' => '375', 'flag' => '🇧🇾'),
            'MD' => array('name' => 'Moldova', 'code' => '373', 'flag' => '🇲🇩'),
            'TR' => array('name' => 'Turkey', 'code' => '90', 'flag' => '🇹🇷'),
            'EG' => array('name' => 'Egypt', 'code' => '20', 'flag' => '🇪🇬'),
            'SA' => array('name' => 'Saudi Arabia', 'code' => '966', 'flag' => '🇸🇦'),
            'AE' => array('name' => 'United Arab Emirates', 'code' => '971', 'flag' => '🇦🇪'),
            'QA' => array('name' => 'Qatar', 'code' => '974', 'flag' => '🇶🇦'),
            'KW' => array('name' => 'Kuwait', 'code' => '965', 'flag' => '🇰🇼'),
            'BH' => array('name' => 'Bahrain', 'code' => '973', 'flag' => '🇧🇭'),
            'OM' => array('name' => 'Oman', 'code' => '968', 'flag' => '🇴🇲'),
            'JO' => array('name' => 'Jordan', 'code' => '962', 'flag' => '🇯🇴'),
            'LB' => array('name' => 'Lebanon', 'code' => '961', 'flag' => '🇱🇧'),
            'SY' => array('name' => 'Syria', 'code' => '963', 'flag' => '🇸🇾'),
            'IQ' => array('name' => 'Iraq', 'code' => '964', 'flag' => '🇮🇶'),
            'IR' => array('name' => 'Iran', 'code' => '98', 'flag' => '🇮🇷'),
            'IL' => array('name' => 'Israel', 'code' => '972', 'flag' => '🇮🇱'),
            'PS' => array('name' => 'Palestine', 'code' => '970', 'flag' => '🇵🇸'),
            'IN' => array('name' => 'India', 'code' => '91', 'flag' => '🇮🇳'),
            'PK' => array('name' => 'Pakistan', 'code' => '92', 'flag' => '🇵🇰'),
            'BD' => array('name' => 'Bangladesh', 'code' => '880', 'flag' => '🇧🇩'),
            'LK' => array('name' => 'Sri Lanka', 'code' => '94', 'flag' => '🇱🇰'),
            'NP' => array('name' => 'Nepal', 'code' => '977', 'flag' => '🇳🇵'),
            'BT' => array('name' => 'Bhutan', 'code' => '975', 'flag' => '🇧🇹'),
            'MV' => array('name' => 'Maldives', 'code' => '960', 'flag' => '🇲🇻'),
            'CN' => array('name' => 'China', 'code' => '86', 'flag' => '🇨🇳'),
            'JP' => array('name' => 'Japan', 'code' => '81', 'flag' => '🇯🇵'),
            'KR' => array('name' => 'South Korea', 'code' => '82', 'flag' => '🇰🇷'),
            'KP' => array('name' => 'North Korea', 'code' => '850', 'flag' => '🇰🇵'),
            'MN' => array('name' => 'Mongolia', 'code' => '976', 'flag' => '🇲🇳'),
            'TW' => array('name' => 'Taiwan', 'code' => '886', 'flag' => '🇹🇼'),
            'HK' => array('name' => 'Hong Kong', 'code' => '852', 'flag' => '🇭🇰'),
            'MO' => array('name' => 'Macau', 'code' => '853', 'flag' => '🇲🇴'),
            'SG' => array('name' => 'Singapore', 'code' => '65', 'flag' => '🇸🇬'),
            'MY' => array('name' => 'Malaysia', 'code' => '60', 'flag' => '🇲🇾'),
            'TH' => array('name' => 'Thailand', 'code' => '66', 'flag' => '🇹🇭'),
            'VN' => array('name' => 'Vietnam', 'code' => '84', 'flag' => '🇻🇳'),
            'PH' => array('name' => 'Philippines', 'code' => '63', 'flag' => '🇵🇭'),
            'ID' => array('name' => 'Indonesia', 'code' => '62', 'flag' => '🇮🇩'),
            'BN' => array('name' => 'Brunei', 'code' => '673', 'flag' => '🇧🇳'),
            'KH' => array('name' => 'Cambodia', 'code' => '855', 'flag' => '🇰🇭'),
            'LA' => array('name' => 'Laos', 'code' => '856', 'flag' => '🇱🇦'),
            'MM' => array('name' => 'Myanmar', 'code' => '95', 'flag' => '🇲🇲'),
            'BR' => array('name' => 'Brazil', 'code' => '55', 'flag' => '🇧🇷'),
            'AR' => array('name' => 'Argentina', 'code' => '54', 'flag' => '🇦🇷'),
            'CL' => array('name' => 'Chile', 'code' => '56', 'flag' => '🇨🇱'),
            'CO' => array('name' => 'Colombia', 'code' => '57', 'flag' => '🇨🇴'),
            'PE' => array('name' => 'Peru', 'code' => '51', 'flag' => '🇵🇪'),
            'VE' => array('name' => 'Venezuela', 'code' => '58', 'flag' => '🇻🇪'),
            'EC' => array('name' => 'Ecuador', 'code' => '593', 'flag' => '🇪🇨'),
            'BO' => array('name' => 'Bolivia', 'code' => '591', 'flag' => '🇧🇴'),
            'PY' => array('name' => 'Paraguay', 'code' => '595', 'flag' => '🇵🇾'),
            'UY' => array('name' => 'Uruguay', 'code' => '598', 'flag' => '🇺🇾'),
            'GY' => array('name' => 'Guyana', 'code' => '592', 'flag' => '🇬🇾'),
            'SR' => array('name' => 'Suriname', 'code' => '597', 'flag' => '🇸🇷'),
            'GF' => array('name' => 'French Guiana', 'code' => '594', 'flag' => '🇬🇫'),
            'MX' => array('name' => 'Mexico', 'code' => '52', 'flag' => '🇲🇽'),
            'GT' => array('name' => 'Guatemala', 'code' => '502', 'flag' => '🇬🇹'),
            'BZ' => array('name' => 'Belize', 'code' => '501', 'flag' => '🇧🇿'),
            'SV' => array('name' => 'El Salvador', 'code' => '503', 'flag' => '🇸🇻'),
            'HN' => array('name' => 'Honduras', 'code' => '504', 'flag' => '🇭🇳'),
            'NI' => array('name' => 'Nicaragua', 'code' => '505', 'flag' => '🇳🇮'),
            'CR' => array('name' => 'Costa Rica', 'code' => '506', 'flag' => '🇨🇷'),
            'PA' => array('name' => 'Panama', 'code' => '507', 'flag' => '🇵🇦'),
            'ZA' => array('name' => 'South Africa', 'code' => '27', 'flag' => '🇿🇦'),
            'NG' => array('name' => 'Nigeria', 'code' => '234', 'flag' => '🇳🇬'),
            'KE' => array('name' => 'Kenya', 'code' => '254', 'flag' => '🇰🇪'),
            'GH' => array('name' => 'Ghana', 'code' => '233', 'flag' => '🇬🇭'),
            'ET' => array('name' => 'Ethiopia', 'code' => '251', 'flag' => '🇪🇹'),
            'TZ' => array('name' => 'Tanzania', 'code' => '255', 'flag' => '🇹🇿'),
            'UG' => array('name' => 'Uganda', 'code' => '256', 'flag' => '🇺🇬'),
            'RW' => array('name' => 'Rwanda', 'code' => '250', 'flag' => '🇷🇼'),
            'MA' => array('name' => 'Morocco', 'code' => '212', 'flag' => '🇲🇦'),
            'DZ' => array('name' => 'Algeria', 'code' => '213', 'flag' => '🇩🇿'),
            'TN' => array('name' => 'Tunisia', 'code' => '216', 'flag' => '🇹🇳'),
            'LY' => array('name' => 'Libya', 'code' => '218', 'flag' => '🇱🇾'),
            'SD' => array('name' => 'Sudan', 'code' => '249', 'flag' => '🇸🇩'),
        );
    }
}

// Initialize AJAX handler
new WhatsApp_OTP_Ajax_Handler();
