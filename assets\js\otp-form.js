/**
 * WhatsApp OTP Login Form JavaScript
 * Handles dynamic UI interactions and AJAX requests
 */

(function($) {
    'use strict';
    
    // Wait for DOM to be ready
    $(document).ready(function() {
        initOTPForm();
    });
    
    /**
     * Initialize OTP form functionality
     */
    function initOTPForm() {
        const $form = $('.whatsapp-otp-form');
        
        if ($form.length === 0) {
            return;
        }
        
        // Initialize event handlers
        initPhoneForm();
        initOTPForm();
        initCountrySelect();
        initBackButton();
        initResendButton();
        
        // Auto-focus first input
        $('#country-select').focus();
    }
    
    /**
     * Initialize phone form submission
     */
    function initPhoneForm() {
        $('#phone-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $button = $('#send-otp-btn');
            const countryCode = $('#country-select').val();
            const phoneNumber = $('#phone-number').val().trim();
            
            // Validate inputs
            if (!countryCode) {
                showMessage('error', whatsapp_otp_ajax.messages.invalid_phone);
                return;
            }
            
            if (!phoneNumber || !isValidPhoneNumber(phoneNumber)) {
                showMessage('error', whatsapp_otp_ajax.messages.invalid_phone);
                return;
            }
            
            // Disable button and show loading
            $button.prop('disabled', true).text(whatsapp_otp_ajax.messages.sending);
            
            // Send AJAX request
            $.ajax({
                url: whatsapp_otp_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'send_whatsapp_otp',
                    nonce: whatsapp_otp_ajax.nonce,
                    country_code: countryCode,
                    phone_number: phoneNumber
                },
                success: function(response) {
                    if (response.success) {
                        showMessage('success', response.data.message);
                        
                        // Store phone number for verification step
                        $('#verified-phone').val(response.data.phone);
                        
                        // Switch to OTP step
                        switchStep('step-otp');
                        
                        // Focus OTP input
                        setTimeout(function() {
                            $('#otp-code').focus();
                        }, 100);
                        
                    } else {
                        showMessage('error', response.data.message || whatsapp_otp_ajax.messages.error);
                    }
                },
                error: function() {
                    showMessage('error', whatsapp_otp_ajax.messages.error);
                },
                complete: function() {
                    // Re-enable button
                    $button.prop('disabled', false).text('Send OTP via WhatsApp');
                }
            });
        });
    }
    
    /**
     * Initialize OTP verification form
     */
    function initOTPForm() {
        $('#otp-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $button = $('#verify-otp-btn');
            const phone = $('#verified-phone').val();
            const otpCode = $('#otp-code').val().trim();
            
            // Validate OTP
            if (!otpCode || !isValidOTP(otpCode)) {
                showMessage('error', whatsapp_otp_ajax.messages.invalid_otp);
                return;
            }
            
            // Disable button and show loading
            $button.prop('disabled', true).text(whatsapp_otp_ajax.messages.verifying);
            
            // Send AJAX request
            $.ajax({
                url: whatsapp_otp_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'verify_whatsapp_otp',
                    nonce: whatsapp_otp_ajax.nonce,
                    phone: phone,
                    otp_code: otpCode
                },
                success: function(response) {
                    if (response.success) {
                        showMessage('success', response.data.message);
                        
                        // Redirect after successful login
                        setTimeout(function() {
                            if (response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                window.location.reload();
                            }
                        }, 1500);
                        
                    } else {
                        showMessage('error', response.data.message || whatsapp_otp_ajax.messages.error);
                    }
                },
                error: function() {
                    showMessage('error', whatsapp_otp_ajax.messages.error);
                },
                complete: function() {
                    // Re-enable button
                    $button.prop('disabled', false).text('Verify & Login');
                }
            });
        });
    }
    
    /**
     * Initialize country select functionality
     */
    function initCountrySelect() {
        $('#country-select').on('change', function() {
            const selectedOption = $(this).find('option:selected');
            const countryCode = selectedOption.data('code');
            
            if (countryCode) {
                // You could add country code prefix to phone input here if needed
                // For now, we'll just ensure the phone input is focused
                $('#phone-number').focus();
            }
        });
        
        // Format phone number input (numbers only)
        $('#phone-number').on('input', function() {
            let value = $(this).val().replace(/[^0-9]/g, '');
            $(this).val(value);
        });
        
        // Format OTP input (numbers only, max 6 digits)
        $('#otp-code').on('input', function() {
            let value = $(this).val().replace(/[^0-9]/g, '').substring(0, 6);
            $(this).val(value);
        });
    }
    
    /**
     * Initialize back button functionality
     */
    function initBackButton() {
        $('#back-to-phone').on('click', function() {
            switchStep('step-phone');
            clearMessages();
            $('#otp-code').val('');
        });
    }
    
    /**
     * Initialize resend button functionality
     */
    function initResendButton() {
        $('#resend-otp').on('click', function() {
            // Switch back to phone step to resend
            switchStep('step-phone');
            clearMessages();
            
            // Auto-submit the phone form if we have the data
            const countryCode = $('#country-select').val();
            const phoneNumber = $('#phone-number').val();
            
            if (countryCode && phoneNumber) {
                setTimeout(function() {
                    $('#phone-form').submit();
                }, 100);
            }
        });
    }
    
    /**
     * Switch between form steps
     */
    function switchStep(stepId) {
        $('.step').removeClass('active');
        $('#' + stepId).addClass('active');
        
        // Clear any existing messages when switching steps
        clearMessages();
    }
    
    /**
     * Show message to user
     */
    function showMessage(type, message) {
        const $messagesContainer = $('#otp-messages');
        const messageClass = type === 'success' ? 'success' : 'error';
        
        const messageHtml = '<div class="message ' + messageClass + '">' + message + '</div>';
        
        $messagesContainer.html(messageHtml);
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                $messagesContainer.find('.message.success').fadeOut();
            }, 5000);
        }
        
        // Scroll to message
        $messagesContainer[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    /**
     * Clear all messages
     */
    function clearMessages() {
        $('#otp-messages').empty();
    }
    
    /**
     * Validate phone number format
     */
    function isValidPhoneNumber(phone) {
        // Remove any non-numeric characters
        const cleanPhone = phone.replace(/[^0-9]/g, '');
        
        // Check if it's between 7 and 15 digits
        return cleanPhone.length >= 7 && cleanPhone.length <= 15;
    }
    
    /**
     * Validate OTP format
     */
    function isValidOTP(otp) {
        // Must be exactly 6 digits
        return /^[0-9]{6}$/.test(otp);
    }
    
    /**
     * Handle Enter key navigation
     */
    $(document).on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            const $activeStep = $('.step.active');
            
            if ($activeStep.attr('id') === 'step-phone') {
                $('#phone-form').submit();
            } else if ($activeStep.attr('id') === 'step-otp') {
                $('#otp-form').submit();
            }
        }
    });
    
    /**
     * Auto-submit OTP when 6 digits are entered
     */
    $('#otp-code').on('input', function() {
        const value = $(this).val();
        if (value.length === 6 && isValidOTP(value)) {
            // Small delay to allow user to see the complete code
            setTimeout(function() {
                $('#otp-form').submit();
            }, 500);
        }
    });
    
    /**
     * Handle paste events for OTP input
     */
    $('#otp-code').on('paste', function(e) {
        setTimeout(function() {
            const value = $('#otp-code').val().replace(/[^0-9]/g, '').substring(0, 6);
            $('#otp-code').val(value);
            
            if (value.length === 6) {
                setTimeout(function() {
                    $('#otp-form').submit();
                }, 500);
            }
        }, 10);
    });
    
    /**
     * Add loading animation to buttons
     */
    function addLoadingAnimation($button, originalText) {
        let dots = '';
        const interval = setInterval(function() {
            dots = dots.length >= 3 ? '' : dots + '.';
            $button.text(originalText + dots);
        }, 500);
        
        // Store interval ID for cleanup
        $button.data('loading-interval', interval);
    }
    
    /**
     * Remove loading animation from buttons
     */
    function removeLoadingAnimation($button, originalText) {
        const interval = $button.data('loading-interval');
        if (interval) {
            clearInterval(interval);
            $button.removeData('loading-interval');
        }
        $button.text(originalText);
    }
    
    /**
     * Enhanced error handling with retry functionality
     */
    function handleAjaxError(xhr, textStatus, errorThrown) {
        let errorMessage = whatsapp_otp_ajax.messages.error;
        
        if (xhr.status === 429) {
            errorMessage = 'Too many requests. Please wait a moment and try again.';
        } else if (xhr.status === 500) {
            errorMessage = 'Server error. Please try again later.';
        } else if (xhr.status === 0) {
            errorMessage = 'Network error. Please check your connection and try again.';
        }
        
        showMessage('error', errorMessage);
    }
    
    /**
     * Add accessibility improvements
     */
    function initAccessibility() {
        // Add ARIA labels
        $('#country-select').attr('aria-label', 'Select your country');
        $('#phone-number').attr('aria-label', 'Enter your phone number');
        $('#otp-code').attr('aria-label', 'Enter the 6-digit verification code');
        
        // Add role attributes
        $('#otp-messages').attr('role', 'alert').attr('aria-live', 'polite');
        
        // Improve focus management
        $('.step').attr('tabindex', '-1');
    }
    
    // Initialize accessibility features
    initAccessibility();
    
})(jQuery);
