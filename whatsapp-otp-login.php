<?php
/**
 * Plugin Name: WhatsApp OTP Login
 * Plugin URI: https://moflavio.xyz
 * Description: Enable user login or registration using OTP sent via WhatsApp based on WooCommerce billing phone number.
 * Version: 1.0.0
 * Author: Flavio
 * Author URI: https://moflavio.xyz
 * Text Domain: whatsapp-otp-login
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WHATSAPP_OTP_VERSION', '1.0.0');
define('WHATSAPP_OTP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WHATSAPP_OTP_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main WhatsApp OTP Login Class
 */
class WhatsApp_OTP_Login {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('whatsapp-otp-login', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Include required files
        $this->include_files();
        
        // Register shortcode
        add_shortcode('whatsapp_otp_login', array($this, 'render_login_form'));
        
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Include required files
     */
    private function include_files() {
        require_once WHATSAPP_OTP_PLUGIN_PATH . 'form-handler.php';
        require_once WHATSAPP_OTP_PLUGIN_PATH . 'ajax-handler.php';
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Only load on pages with the shortcode
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'whatsapp_otp_login')) {
            wp_enqueue_script(
                'whatsapp-otp-form',
                WHATSAPP_OTP_PLUGIN_URL . 'assets/js/otp-form.js',
                array('jquery'),
                WHATSAPP_OTP_VERSION,
                true
            );
            
            wp_localize_script('whatsapp-otp-form', 'whatsapp_otp_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('whatsapp_otp_nonce'),
                'messages' => array(
                    'sending' => __('Sending OTP...', 'whatsapp-otp-login'),
                    'verifying' => __('Verifying OTP...', 'whatsapp-otp-login'),
                    'error' => __('An error occurred. Please try again.', 'whatsapp-otp-login'),
                    'invalid_phone' => __('Please enter a valid phone number.', 'whatsapp-otp-login'),
                    'invalid_otp' => __('Please enter a valid 6-digit OTP.', 'whatsapp-otp-login')
                )
            ));
            
            // Add inline CSS for styling
            wp_add_inline_style('wp-block-library', $this->get_inline_css());
        }
    }
    
    /**
     * Get inline CSS for the form
     */
    private function get_inline_css() {
        return '
        .whatsapp-otp-form {
            max-width: 400px;
            margin: 20px auto;
            padding: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .whatsapp-otp-form h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #25D366;
        }
        .whatsapp-otp-form .form-group {
            margin-bottom: 20px;
        }
        .whatsapp-otp-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .whatsapp-otp-form select,
        .whatsapp-otp-form input[type="tel"],
        .whatsapp-otp-form input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .whatsapp-otp-form .phone-input-group {
            display: flex;
            gap: 10px;
        }
        .whatsapp-otp-form .country-select {
            flex: 1;
        }
        .whatsapp-otp-form .phone-input {
            flex: 2;
        }
        .whatsapp-otp-form button {
            width: 100%;
            padding: 12px;
            background: #25D366;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .whatsapp-otp-form button:hover {
            background: #128C7E;
        }
        .whatsapp-otp-form button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .whatsapp-otp-form .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }
        .whatsapp-otp-form .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .whatsapp-otp-form .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .whatsapp-otp-form .step {
            display: none;
        }
        .whatsapp-otp-form .step.active {
            display: block;
        }
        .whatsapp-otp-form .back-btn {
            background: #6c757d;
            margin-bottom: 10px;
        }
        .whatsapp-otp-form .back-btn:hover {
            background: #5a6268;
        }
        @media (max-width: 480px) {
            .whatsapp-otp-form {
                margin: 10px;
                padding: 20px;
            }
            .whatsapp-otp-form .phone-input-group {
                flex-direction: column;
            }
        }';
    }
    
    /**
     * Render login form shortcode
     */
    public function render_login_form($atts) {
        // Don't show form if user is already logged in
        if (is_user_logged_in()) {
            return '<p>' . __('You are already logged in.', 'whatsapp-otp-login') . '</p>';
        }
        
        $form_handler = new WhatsApp_OTP_Form_Handler();
        return $form_handler->render_form();
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('WhatsApp OTP Settings', 'whatsapp-otp-login'),
            __('WhatsApp OTP', 'whatsapp-otp-login'),
            'manage_options',
            'whatsapp-otp-settings',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('whatsapp_otp_settings', 'whatsapp_otp_api_session');
        register_setting('whatsapp_otp_settings', 'whatsapp_otp_api_url');
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('WhatsApp OTP Settings', 'whatsapp-otp-login'); ?></h1>
            <form method="post" action="options.php">
                <?php settings_fields('whatsapp_otp_settings'); ?>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('API Base URL', 'whatsapp-otp-login'); ?></th>
                        <td>
                            <input type="url" name="whatsapp_otp_api_url" value="<?php echo esc_attr(get_option('whatsapp_otp_api_url', '')); ?>" class="regular-text" />
                            <p class="description"><?php _e('Enter the base URL for your WhatsApp API (without /message/send-text)', 'whatsapp-otp-login'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Session', 'whatsapp-otp-login'); ?></th>
                        <td>
                            <input type="text" name="whatsapp_otp_api_session" value="<?php echo esc_attr(get_option('whatsapp_otp_api_session', '')); ?>" class="regular-text" />
                            <p class="description"><?php _e('Enter your WhatsApp API session token', 'whatsapp-otp-login'); ?></p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create log table if needed
        $this->create_log_table();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_whatsapp_otp_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_whatsapp_otp_%'");
    }
    
    /**
     * Create log table
     */
    private function create_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'whatsapp_otp_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            phone varchar(20) NOT NULL,
            action varchar(20) NOT NULL,
            status varchar(10) NOT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY phone (phone),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
new WhatsApp_OTP_Login();
